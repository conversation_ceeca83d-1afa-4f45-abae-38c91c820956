package com.ebon.energy.fms.domain.vo.site;

import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.ebon.energy.fms.domain.entity.ProductDailyHistoryDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

/**
 * Copyright (c) Redback Technologies. All Rights Reserved.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductWithDailyCache {

    private String serialNumber;

    private BaseAboutDevice baseAboutDevice;

    private InstallationSpecification installationSpecification;

    private List<ProductDailyHistoryDO> dailyCaches;
}
